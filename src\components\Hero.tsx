import React from "react";
import { <PERSON><PERSON> } from "./ui/button";
import { ArrowRight, ArrowDown } from "lucide-react";
import { <PERSON> } from "react-router-dom";

const Hero = () => {
  const handleSmoothScroll = (e: React.MouseEvent<HTMLAnchorElement | HTMLButtonElement>, id: string) => {
    e.preventDefault();

    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  };

  return (
    <section
      id="hero"
      className="relative h-screen flex items-center justify-center overflow-hidden"
      style={{
        backgroundImage: "url('/uploads/temp.png')",
        backgroundSize: "cover",
        backgroundPosition: "center"
      }}
    >
      <div className="absolute inset-0 bg-black/60 backdrop-blur-sm"></div>
      <div className="relative z-10 text-center px-4 max-w-4xl mx-auto">
        <h1 className="text-5xl md:text-7xl lg:text-8xl text-white font-trajan mb-10 max-w-4xl mx-auto">
          Een podium voor kunst met impact.
        </h1>

        <div className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6">
          <Button
            size="lg"
            className="bg-white text-ouroboros-accent hover:bg-white/90 group"
            onClick={(e) => handleSmoothScroll(e, 'about')}
          >
            <span>Ontdek meer</span>
            {/* <ArrowDown className="ml-2 h-5 w-5 transition-transform group-hover:translate-y-1" /> */}
          </Button>

          <Link
            to="/projects"
            onClick={(e) => handleSmoothScroll(e, 'projects')}
            className="ml-5 flex items-center text-white hover:text-white/80 transition-colors group text-right"
          >
            <span>Ontdek onze projecten</span>
            <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
          </Link>

          
        </div>
      </div>
    </section>
  );
};

export default Hero;
